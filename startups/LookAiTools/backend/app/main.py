#!/usr/bin/env python3
"""
AI工具导航站后端API
FastAPI应用主入口
"""

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import asyncpg
import json
import os
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = FastAPI(
    title="AI Tools Navigator API",
    description="AI工具导航站后端API",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],  # 前端开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库连接池
db_pool = None

# Pydantic模型
class BilingualText(BaseModel):
    en: str
    cn: str

class AIToolResponse(BaseModel):
    id: int
    name: BilingualText
    title: BilingualText
    slug: str
    url: str
    page_screenshot: Optional[str] = None
    description: BilingualText
    long_description: Optional[BilingualText] = None
    key_features: Optional[List[BilingualText]] = []
    use_cases: Optional[BilingualText] = None
    target_audience: Optional[BilingualText] = None
    category: str
    subcategory: Optional[BilingualText] = None
    tags: Optional[List[BilingualText]] = []
    industry_tags: Optional[List[BilingualText]] = []
    pricing_type: Optional[BilingualText] = None
    pricing_details: Optional[Dict[str, Any]] = {}
    trial_available: Optional[BilingualText] = None
    rating: Optional[float] = 0
    view_count: Optional[int] = 0
    traffic_estimate: Optional[int] = 0
    featured: Optional[bool] = False
    status: Optional[str] = "active"
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class CategoryResponse(BaseModel):
    id: str
    name: str
    slug: str
    description: Optional[str] = None
    icon: Optional[str] = None
    count: Optional[int] = 0

class PaginationResponse(BaseModel):
    page: int
    limit: int
    total: int
    total_pages: int

class APIResponse(BaseModel):
    data: Any
    pagination: Optional[PaginationResponse] = None
    success: bool = True
    message: Optional[str] = None

# 数据库连接管理
async def get_db_connection():
    """获取数据库连接"""
    global db_pool
    if db_pool is None:
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            raise HTTPException(status_code=500, detail="数据库连接配置错误")
        db_pool = await asyncpg.create_pool(database_url)
    return db_pool

async def close_db_connection():
    """关闭数据库连接"""
    global db_pool
    if db_pool:
        await db_pool.close()

# 启动和关闭事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库连接"""
    await get_db_connection()
    print("✓ 数据库连接池已初始化")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    await close_db_connection()
    print("✓ 数据库连接池已关闭")

# 工具函数
def parse_jsonb_field(value: str) -> Any:
    """解析JSONB字段"""
    if value is None:
        return None
    try:
        return json.loads(value)
    except (json.JSONDecodeError, TypeError):
        return value

def format_tool_response(tool_row: dict, language: str = "en") -> dict:
    """格式化工具响应数据"""
    # 解析JSONB字段
    name = parse_jsonb_field(tool_row.get('name', '{}'))
    title = parse_jsonb_field(tool_row.get('title', '{}'))
    description = parse_jsonb_field(tool_row.get('description', '{}'))
    long_description = parse_jsonb_field(tool_row.get('long_description', '{}'))
    key_features = parse_jsonb_field(tool_row.get('key_features', '[]'))
    use_cases = parse_jsonb_field(tool_row.get('use_cases', '{}'))
    target_audience = parse_jsonb_field(tool_row.get('target_audience', '{}'))
    subcategory = parse_jsonb_field(tool_row.get('subcategory', '{}'))
    tags = parse_jsonb_field(tool_row.get('tags', '[]'))
    industry_tags = parse_jsonb_field(tool_row.get('industry_tags', '[]'))
    pricing_type = parse_jsonb_field(tool_row.get('pricing_type', '{}'))
    pricing_details = parse_jsonb_field(tool_row.get('pricing_details', '{}'))
    trial_available = parse_jsonb_field(tool_row.get('trial_available', '{}'))

    # 辅助函数：根据语言获取文本
    def get_localized_text(text_obj, lang):
        if not text_obj:
            return ''
        if isinstance(text_obj, dict):
            # 支持 zh, zh-CN, zh-Hans 等中文变体
            if lang.startswith('zh'):
                return text_obj.get('cn', text_obj.get('zh', text_obj.get('en', '')))
            else:
                return text_obj.get('en', text_obj.get('cn', ''))
        return str(text_obj)

    # 为前端兼容性，提供简化的字段
    return {
        "id": str(tool_row.get('id')),
        "name": get_localized_text(name, language),
        "title": get_localized_text(title, language),
        "description": get_localized_text(description, language),
        "url": tool_row.get('url', ''),
        "thumbnail_url": tool_row.get('page_screenshot', ''),
        "category": tool_row.get('category', ''),
        "tags": [get_localized_text(tag, language) for tag in tags] if isinstance(tags, list) else [],
        "pricing": get_localized_text(pricing_type, language).lower() if pricing_type else 'unknown',
        "featured": tool_row.get('featured', False),
        "traffic": tool_row.get('traffic_estimate', 0),
        "created_at": str(tool_row.get('created_at', '')),
        "updated_at": str(tool_row.get('updated_at', '')),
        # 完整的双语数据
        "full_data": {
            "name": name,
            "title": title,
            "description": description,
            "long_description": long_description,
            "key_features": key_features,
            "use_cases": use_cases,
            "target_audience": target_audience,
            "subcategory": subcategory,
            "tags": tags,
            "industry_tags": industry_tags,
            "pricing_type": pricing_type,
            "pricing_details": pricing_details,
            "trial_available": trial_available,
            "rating": tool_row.get('rating', 0),
            "view_count": tool_row.get('view_count', 0),
            "traffic_estimate": tool_row.get('traffic_estimate', 0),
            "featured": tool_row.get('featured', False),
            "status": tool_row.get('status', 'active'),
            "slug": tool_row.get('slug', ''),
            "page_screenshot": tool_row.get('page_screenshot', ''),
        }
    }

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI Tools Navigator API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

@app.get("/api/tools")
async def get_tools(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(12, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    featured: Optional[bool] = Query(None, description="是否精选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    language: str = Query("en", description="语言")
):
    """获取工具列表"""
    try:
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            # 构建查询条件
            where_conditions = ["status = 'active'"]
            params = []
            param_count = 0
            
            if category:
                param_count += 1
                where_conditions.append(f"category = ${param_count}")
                params.append(category)
            
            if featured is not None:
                param_count += 1
                where_conditions.append(f"featured = ${param_count}")
                params.append(featured)
            
            if search:
                param_count += 1
                where_conditions.append(f"""
                    (title::text ILIKE ${param_count} OR 
                     description::text ILIKE ${param_count} OR 
                     category ILIKE ${param_count})
                """)
                params.append(f"%{search}%")
            
            where_clause = " AND ".join(where_conditions)
            
            # 获取总数
            count_query = f"SELECT COUNT(*) FROM ai_tools WHERE {where_clause}"
            total = await conn.fetchval(count_query, *params)
            
            # 获取数据
            offset = (page - 1) * limit
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count
            
            query = f"""
                SELECT * FROM ai_tools 
                WHERE {where_clause}
                ORDER BY featured DESC, created_at DESC
                LIMIT ${limit_param} OFFSET ${offset_param}
            """
            params.extend([limit, offset])
            
            rows = await conn.fetch(query, *params)
            
            # 格式化响应
            tools = [format_tool_response(dict(row), language) for row in rows]
            
            total_pages = (total + limit - 1) // limit
            
            return APIResponse(
                data=tools,
                pagination=PaginationResponse(
                    page=page,
                    limit=limit,
                    total=total,
                    total_pages=total_pages
                )
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

@app.get("/api/tools/{tool_identifier}")
async def get_tool(tool_identifier: str, language: str = Query("en", description="语言")):
    """获取单个工具详情 - 支持ID或slug"""
    try:
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            # 尝试通过slug查找，如果失败则通过ID查找
            if tool_identifier.isdigit():
                # 如果是数字，按ID查找
                query = "SELECT * FROM ai_tools WHERE id = $1 AND status = 'active'"
                row = await conn.fetchrow(query, int(tool_identifier))
            else:
                # 如果不是数字，按slug查找
                query = "SELECT * FROM ai_tools WHERE slug = $1 AND status = 'active'"
                row = await conn.fetchrow(query, tool_identifier)

            if not row:
                raise HTTPException(status_code=404, detail="工具不存在")

            tool = format_tool_response(dict(row), language)

            return APIResponse(data=tool)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工具详情失败: {str(e)}")

@app.get("/api/tools/{tool_id}/related")
async def get_related_tools(tool_id: str, language: str = Query("en", description="语言"), limit: int = Query(4, description="返回数量")):
    """获取相关工具"""
    try:
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            # 首先获取当前工具信息
            if tool_id.isdigit():
                current_tool_query = "SELECT category FROM ai_tools WHERE id = $1 AND status = 'active'"
                current_tool = await conn.fetchrow(current_tool_query, int(tool_id))
            else:
                current_tool_query = "SELECT category FROM ai_tools WHERE slug = $1 AND status = 'active'"
                current_tool = await conn.fetchrow(current_tool_query, tool_id)

            if not current_tool:
                raise HTTPException(status_code=404, detail="工具不存在")

            # 获取同分类的其他工具
            if tool_id.isdigit():
                query = """
                    SELECT * FROM ai_tools
                    WHERE category = $1 AND id != $2 AND status = 'active'
                    ORDER BY featured DESC, view_count DESC, created_at DESC
                    LIMIT $3
                """
                rows = await conn.fetch(query, current_tool['category'], int(tool_id), limit)
            else:
                query = """
                    SELECT * FROM ai_tools
                    WHERE category = $1 AND slug != $2 AND status = 'active'
                    ORDER BY featured DESC, view_count DESC, created_at DESC
                    LIMIT $3
                """
                rows = await conn.fetch(query, current_tool['category'], tool_id, limit)

            # 如果同分类工具不够，补充其他工具
            if len(rows) < limit:
                remaining = limit - len(rows)
                if tool_id.isdigit():
                    additional_query = """
                        SELECT * FROM ai_tools
                        WHERE category != $1 AND id != $2 AND status = 'active'
                        ORDER BY featured DESC, view_count DESC, created_at DESC
                        LIMIT $3
                    """
                    additional_rows = await conn.fetch(additional_query, current_tool['category'], int(tool_id), remaining)
                else:
                    additional_query = """
                        SELECT * FROM ai_tools
                        WHERE category != $1 AND slug != $2 AND status = 'active'
                        ORDER BY featured DESC, view_count DESC, created_at DESC
                        LIMIT $3
                    """
                    additional_rows = await conn.fetch(additional_query, current_tool['category'], tool_id, remaining)
                rows.extend(additional_rows)

            # 格式化响应
            tools = [format_tool_response(dict(row), language) for row in rows]

            return APIResponse(data=tools)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取相关工具失败: {str(e)}")

@app.get("/api/categories")
async def get_categories(language: str = Query("en", description="语言")):
    """获取分类列表"""
    try:
        pool = await get_db_connection()
        async with pool.acquire() as conn:
            # 获取所有分类及其工具数量
            query = """
                SELECT 
                    category,
                    COUNT(*) as count
                FROM ai_tools 
                WHERE status = 'active'
                GROUP BY category
                ORDER BY count DESC
            """
            rows = await conn.fetch(query)
            
            # 格式化分类数据
            categories = []
            for row in rows:
                category_name = row['category']
                categories.append({
                    "id": category_name,
                    "name": category_name.replace('-', ' ').title(),
                    "slug": category_name,
                    "count": row['count']
                })
            
            return APIResponse(data=categories)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
