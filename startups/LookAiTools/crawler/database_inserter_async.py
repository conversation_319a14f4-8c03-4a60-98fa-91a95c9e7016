#!/usr/bin/env python3
"""
异步数据库插入脚本 - 多语言架构版本
将标准化的JSON数据插入到多语言PostgreSQL数据库中
支持分类翻译、工具翻译、标签翻译等多语言表
使用asyncpg驱动
"""

import json
import os
import sys
import asyncio
import re
from typing import Dict, Any, List, Optional, Tuple
import asyncpg
from dotenv import load_dotenv
from urllib.parse import urlparse

# 加载环境变量
load_dotenv()

class AsyncDatabaseInserter:
    def __init__(self):
        """初始化数据库连接"""
        self.connection = None
        self.category_cache = {}  # 缓存分类ID
        self.tag_cache = {}       # 缓存标签ID

    async def connect_to_database(self):
        """连接到PostgreSQL数据库"""
        try:
            database_url = os.getenv('DATABASE_URL')
            if not database_url:
                raise ValueError("DATABASE_URL环境变量未设置")

            self.connection = await asyncpg.connect(database_url)
            print("✓ 数据库连接成功")

            # 加载分类和标签缓存
            await self.load_category_cache()
            await self.load_tag_cache()

        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            sys.exit(1)

    async def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            await self.connection.close()
        print("✓ 数据库连接已关闭")
    
    async def load_category_cache(self):
        """加载分类缓存"""
        try:
            categories = await self.connection.fetch("SELECT id, category_key FROM categories")
            self.category_cache = {cat['category_key']: cat['id'] for cat in categories}
            print(f"✓ 加载了 {len(self.category_cache)} 个分类")
        except Exception as e:
            print(f"✗ 加载分类缓存失败: {e}")

    async def load_tag_cache(self):
        """加载标签缓存"""
        try:
            tags = await self.connection.fetch("SELECT id, tag_key FROM tags")
            self.tag_cache = {tag['tag_key']: tag['id'] for tag in tags}
            print(f"✓ 加载了 {len(self.tag_cache)} 个标签")
        except Exception as e:
            print(f"✗ 加载标签缓存失败: {e}")

    async def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            result = await self.connection.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = $1
                );
            """, table_name)
            return result
        except Exception as e:
            print(f"检查表存在性失败: {e}")
            return False
    
    async def execute_schema_file(self, schema_file_path: str):
        """执行数据库架构文件"""
        try:
            # 读取SQL文件
            with open(schema_file_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()

            # 直接执行整个SQL文件
            try:
                await self.connection.execute(schema_sql)
                print("✓ 数据库架构创建完成")
            except Exception as e:
                print(f"✗ 执行架构文件失败: {e}")
                # 如果整体执行失败，尝试分段执行
                print("尝试分段执行SQL语句...")

                # 按照双分号分割语句（处理函数定义）
                statements = []
                current_statement = ""
                in_function = False

                for line in schema_sql.split('\n'):
                    line = line.strip()
                    if not line or line.startswith('--'):
                        continue

                    current_statement += line + '\n'

                    if '$$' in line:
                        in_function = not in_function

                    if line.endswith(';') and not in_function:
                        statements.append(current_statement.strip())
                        current_statement = ""

                for i, statement in enumerate(statements):
                    if statement:
                        try:
                            await self.connection.execute(statement)
                            print(f"✓ 执行语句 {i+1}/{len(statements)}")
                        except Exception as e:
                            print(f"✗ 语句执行失败 {i+1}: {e}")

        except Exception as e:
            print(f"✗ 执行架构文件失败: {e}")
            raise

    def convert_to_s3_path(self, local_path: str) -> str:
        """将本地截图路径转换为S3路径"""
        if not local_path:
            return ''

        # 提取文件名
        import os
        filename = os.path.basename(local_path)

        # 构建S3 URL
        s3_base_url = "https://lookforai.s3.us-east-1.amazonaws.com/aitools"
        s3_path = f"{s3_base_url}/{filename}"

        return s3_path

    def normalize_category_key(self, category: str) -> str:
        """规范化分类key"""
        if not category:
            return 'other'

        # 转换为小写并替换特殊字符
        key = category.lower().strip()
        key = re.sub(r'[&\s]+', '-', key)  # 替换&和空格为-
        key = re.sub(r'[^\w\-]', '', key)  # 移除其他特殊字符
        key = re.sub(r'-+', '-', key)      # 合并多个-
        key = key.strip('-')               # 移除首尾的-

        # 映射已知分类
        category_mapping = {
            'code-it': 'productivity',
            'codeit': 'productivity',
            'code&it': 'productivity',
            'text-writing': 'productivity',
            'textwriting': 'productivity',
            'text&writing': 'productivity',
            'design-art': 'design',
            'designart': 'design',
            'design&art': 'design',
            'life-assistant': 'productivity',
            'lifeassistant': 'productivity',
            'ai-tools-directory': 'ai-tools',
            'aitoolsdirectory': 'ai-tools',
            'ai-detector': 'ai-tools',
            'aidetector': 'ai-tools',
            'productivity': 'productivity',
            'ai-tools': 'ai-tools',
            'design': 'design'
        }

        return category_mapping.get(key, 'productivity')

    def create_tag_key(self, tag_name: str) -> str:
        """创建标签key"""
        if not tag_name:
            return ''

        key = tag_name.lower().strip()
        key = re.sub(r'[^\w\s]', '', key)  # 移除特殊字符
        key = re.sub(r'\s+', '-', key)     # 空格替换为-
        key = key.strip('-')

        return key

    async def get_or_create_category(self, category_key: str) -> Optional[int]:
        """获取或创建分类，返回分类ID"""
        if not category_key:
            category_key = 'productivity'

        # 规范化分类key
        normalized_key = self.normalize_category_key(category_key)

        # 检查缓存
        if normalized_key in self.category_cache:
            return self.category_cache[normalized_key]

        # 尝试从数据库获取
        try:
            category_id = await self.connection.fetchval(
                "SELECT id FROM categories WHERE category_key = $1", normalized_key
            )

            if category_id:
                self.category_cache[normalized_key] = category_id
                return category_id

            # 如果不存在，创建新分类
            print(f"⚠️  分类 '{normalized_key}' 不存在，将创建新分类")

            category_id = await self.connection.fetchval("""
                INSERT INTO categories (category_key, sort_order)
                VALUES ($1, 99)
                RETURNING id
            """, normalized_key)

            # 创建分类翻译
            await self.connection.execute("""
                INSERT INTO category_translations (category_id, language_code, category_name, category_description)
                VALUES ($1, 'en', $2, $3), ($1, 'cn', $4, $5)
            """, category_id,
                normalized_key.replace('-', ' ').title(),
                f"{normalized_key.replace('-', ' ').title()} tools and applications",
                normalized_key.replace('-', ' '),
                f"{normalized_key.replace('-', ' ')}工具和应用")

            self.category_cache[normalized_key] = category_id
            print(f"✓ 创建新分类: {normalized_key} (ID: {category_id})")

            return category_id

        except Exception as e:
            print(f"✗ 获取或创建分类失败: {e}")
            return None

    async def get_or_create_tag(self, tag_name: str, tag_type: str = 'general') -> Optional[int]:
        """获取或创建标签，返回标签ID"""
        if not tag_name:
            return None

        # 创建标签key
        tag_key = self.create_tag_key(tag_name)
        if not tag_key:
            return None

        # 检查缓存
        if tag_key in self.tag_cache:
            return self.tag_cache[tag_key]

        # 尝试从数据库获取
        try:
            tag_id = await self.connection.fetchval(
                "SELECT id FROM tags WHERE tag_key = $1",
                tag_key
            )

            if tag_id:
                self.tag_cache[tag_key] = tag_id
                return tag_id

            # 创建新标签
            tag_id = await self.connection.fetchval("""
                INSERT INTO tags (tag_key)
                VALUES ($1)
                RETURNING id
            """, tag_key)

            # 创建标签翻译
            await self.connection.execute("""
                INSERT INTO tag_translations (tag_id, language_code, tag_name)
                VALUES ($1, 'en', $2), ($1, 'cn', $3)
            """, tag_id, tag_name, tag_name)

            self.tag_cache[tag_key] = tag_id
            return tag_id

        except Exception as e:
            print(f"✗ 获取或创建标签失败: {e}")
            return None

    async def insert_tool_data(self, tool_data: Dict[str, Any]) -> bool:
        """插入单个工具数据到多语言表结构"""
        try:
            # 开始事务
            async with self.connection.transaction():
                # 1. 提取和处理基础数据
                name_data = tool_data.get('name', {})
                title_data = tool_data.get('title', {})
                description_data = tool_data.get('description', {})
                long_description_data = tool_data.get('long_description', {})

                # 提取双语字段
                name_en = name_data.get('en', '') if isinstance(name_data, dict) else str(name_data)
                name_cn = name_data.get('cn', name_en) if isinstance(name_data, dict) else str(name_data)
                title_en = title_data.get('en', name_en) if isinstance(title_data, dict) else str(title_data)
                title_cn = title_data.get('cn', name_cn) if isinstance(title_data, dict) else str(title_data)
                description_en = description_data.get('en', '') if isinstance(description_data, dict) else str(description_data)
                description_cn = description_data.get('cn', description_en) if isinstance(description_data, dict) else str(description_data)
                long_description_en = long_description_data.get('en', '') if isinstance(long_description_data, dict) else str(long_description_data or '')
                long_description_cn = long_description_data.get('cn', long_description_en) if isinstance(long_description_data, dict) else str(long_description_data or '')

                # 处理定价类型
                pricing_data = tool_data.get('pricing_type', {})
                if isinstance(pricing_data, dict):
                    pricing_type = pricing_data.get('en', 'freemium').lower()
                else:
                    pricing_type = str(pricing_data).lower() if pricing_data else 'freemium'

                # 规范化定价类型
                if 'free' in pricing_type and 'freemium' not in pricing_type:
                    pricing_type = 'free'
                elif 'freemium' in pricing_type:
                    pricing_type = 'freemium'
                elif 'paid' in pricing_type or 'premium' in pricing_type:
                    pricing_type = 'paid'
                else:
                    pricing_type = 'freemium'

                # 处理试用信息
                trial_data = tool_data.get('trial_available', {})
                has_trial = False
                if isinstance(trial_data, dict):
                    trial_text = trial_data.get('en', '').lower()
                    has_trial = 'yes' in trial_text or 'true' in trial_text
                elif isinstance(trial_data, bool):
                    has_trial = trial_data

                # 2. 获取分类ID
                category_key = tool_data.get('category', 'productivity')
                category_id = await self.get_or_create_category(category_key)
                if not category_id:
                    print(f"✗ 无法获取分类ID: {category_key}")
                    return False

                # 3. 插入主工具记录
                tool_id = await self.connection.fetchval("""
                    INSERT INTO tools (
                        slug, url, page_screenshot, category_id, pricing_type, trial_available,
                        rating, view_count, traffic_estimate, featured, status
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
                    ) RETURNING id
                """,
                    tool_data.get('slug', ''),
                    tool_data.get('url', ''),
                    self.convert_to_s3_path(tool_data.get('page_screenshot', '')),
                    category_id,
                    pricing_type,
                    has_trial,
                    tool_data.get('rating', 0),
                    tool_data.get('view_count', 0),
                    tool_data.get('traffic_estimate', 0),
                    tool_data.get('featured', False),
                    tool_data.get('status', 'active')
                )

                print(f"  ✓ 插入工具主记录 (ID: {tool_id})")

                # 4. 插入工具翻译信息
                await self.insert_tool_translations(tool_id, tool_data, name_en, name_cn, title_en, title_cn,
                                                   description_en, description_cn, long_description_en, long_description_cn)

                # 5. 处理标签关联
                await self.insert_tool_tags(tool_id, tool_data)

                # 6. 处理功能特性
                await self.insert_tool_features(tool_id, tool_data)

                # 7. 处理元数据
                await self.insert_tool_metadata(tool_id, tool_data)

                return True

        except asyncpg.UniqueViolationError as e:
            print(f"✗ 数据重复错误: {e}")
            return False
        except Exception as e:
            print(f"✗ 插入数据失败: {e}")
            return False

    async def insert_tool_translations(self, tool_id: int, tool_data: Dict[str, Any],
                                     name_en: str, name_cn: str, title_en: str, title_cn: str,
                                     description_en: str, description_cn: str,
                                     long_description_en: str, long_description_cn: str):
        """插入工具翻译信息"""
        try:
            # 处理使用场景和目标用户
            use_cases_data = tool_data.get('use_cases', {})
            target_audience_data = tool_data.get('target_audience', {})
            subcategory_data = tool_data.get('subcategory', {})

            use_cases_en = use_cases_data.get('en', '') if isinstance(use_cases_data, dict) else str(use_cases_data or '')
            use_cases_cn = use_cases_data.get('cn', use_cases_en) if isinstance(use_cases_data, dict) else str(use_cases_data or '')

            target_audience_en = target_audience_data.get('en', '') if isinstance(target_audience_data, dict) else str(target_audience_data or '')
            target_audience_cn = target_audience_data.get('cn', target_audience_en) if isinstance(target_audience_data, dict) else str(target_audience_data or '')

            subcategory_en = subcategory_data.get('en', '') if isinstance(subcategory_data, dict) else str(subcategory_data or '')
            subcategory_cn = subcategory_data.get('cn', subcategory_en) if isinstance(subcategory_data, dict) else str(subcategory_data or '')

            # 插入英文翻译
            await self.connection.execute("""
                INSERT INTO tool_translations (
                    tool_id, language_code, name, title, description, long_description,
                    use_cases, target_audience, subcategory
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """, tool_id, 'en', name_en, title_en, description_en, long_description_en,
                use_cases_en, target_audience_en, subcategory_en)

            # 插入中文翻译
            await self.connection.execute("""
                INSERT INTO tool_translations (
                    tool_id, language_code, name, title, description, long_description,
                    use_cases, target_audience, subcategory
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """, tool_id, 'cn', name_cn, title_cn, description_cn, long_description_cn,
                use_cases_cn, target_audience_cn, subcategory_cn)

            print(f"  ✓ 插入工具翻译完成")

        except Exception as e:
            print(f"  ✗ 插入工具翻译失败: {e}")

    async def insert_tool_tags(self, tool_id: int, tool_data: Dict[str, Any]):
        """插入工具标签关联"""
        try:
            # 处理普通标签
            tags = tool_data.get('tags', [])
            if isinstance(tags, list):
                for tag_data in tags:
                    if isinstance(tag_data, dict):
                        tag_name = tag_data.get('en', '') or tag_data.get('cn', '')
                    else:
                        tag_name = str(tag_data)

                    if tag_name:
                        tag_id = await self.get_or_create_tag(tag_name, 'general')
                        if tag_id:
                            await self.connection.execute("""
                                INSERT INTO tool_tags (tool_id, tag_id, tag_type)
                                VALUES ($1, $2, $3)
                                ON CONFLICT DO NOTHING
                            """, tool_id, tag_id, 'general')

            # 处理行业标签
            industry_tags = tool_data.get('industry_tags', [])
            if isinstance(industry_tags, list):
                for tag_data in industry_tags:
                    if isinstance(tag_data, dict):
                        tag_name = tag_data.get('en', '') or tag_data.get('cn', '')
                    else:
                        tag_name = str(tag_data)

                    if tag_name:
                        tag_id = await self.get_or_create_tag(tag_name, 'industry')
                        if tag_id:
                            await self.connection.execute("""
                                INSERT INTO tool_tags (tool_id, tag_id, tag_type)
                                VALUES ($1, $2, $3)
                                ON CONFLICT DO NOTHING
                            """, tool_id, tag_id, 'industry')

            print(f"  ✓ 处理标签完成")

        except Exception as e:
            print(f"  ✗ 插入标签失败: {e}")

    async def insert_tool_features(self, tool_id: int, tool_data: Dict[str, Any]):
        """插入工具功能特性"""
        try:
            features = tool_data.get('key_features', [])
            if isinstance(features, list):
                for i, feature_data in enumerate(features):
                    if isinstance(feature_data, dict):
                        feature_en = feature_data.get('en', '')
                        feature_cn = feature_data.get('cn', feature_en)
                    else:
                        feature_en = str(feature_data)
                        feature_cn = feature_en

                    if feature_en:
                        # 插入英文特性
                        await self.connection.execute("""
                            INSERT INTO tool_features (tool_id, language_code, feature_text, sort_order)
                            VALUES ($1, $2, $3, $4)
                        """, tool_id, 'en', feature_en, i)

                        # 插入中文特性
                        await self.connection.execute("""
                            INSERT INTO tool_features (tool_id, language_code, feature_text, sort_order)
                            VALUES ($1, $2, $3, $4)
                        """, tool_id, 'cn', feature_cn, i)

            print(f"  ✓ 插入功能特性完成")

        except Exception as e:
            print(f"  ✗ 插入功能特性失败: {e}")

    async def insert_tool_metadata(self, tool_id: int, tool_data: Dict[str, Any]):
        """插入工具元数据"""
        try:
            meta_info = tool_data.get('meta_info', {})
            source_site = meta_info.get('source_site', 'toolify') if isinstance(meta_info, dict) else 'toolify'

            from datetime import datetime, timezone

            current_time = datetime.now(timezone.utc)

            await self.connection.execute("""
                INSERT INTO tool_metadata (
                    tool_id, source_site, crawl_timestamp, processing_timestamp, model_input_data
                ) VALUES ($1, $2, $3, $4, $5)
            """, tool_id, source_site, None, current_time, json.dumps(tool_data))

            print(f"  ✓ 插入元数据完成")

        except Exception as e:
            print(f"  ✗ 插入元数据失败: {e}")
    
    async def load_and_insert_json_file(self, json_file_path: str) -> Dict[str, int]:
        """加载JSON文件并插入数据库"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✓ 成功加载JSON文件: {json_file_path}")
            
            # 检查数据格式
            if isinstance(data, dict):
                # 单个工具数据
                tools = [data]
            elif isinstance(data, list):
                # 工具数组
                tools = data
            else:
                print("✗ 不支持的JSON格式")
                return {'success': 0, 'failed': 0}
            
            success_count = 0
            failed_count = 0
            
            for i, tool in enumerate(tools, 1):
                tool_title = tool.get('title', {})
                if isinstance(tool_title, dict):
                    display_name = tool_title.get('en', 'Unknown')
                else:
                    display_name = str(tool_title)
                
                print(f"正在插入第 {i}/{len(tools)} 个工具: {display_name}")
                
                if await self.insert_tool_data(tool):
                    success_count += 1
                    print(f"  ✓ 插入成功")
                else:
                    failed_count += 1
                    print(f"  ✗ 插入失败")
            
            return {'success': success_count, 'failed': failed_count}
            
        except Exception as e:
            print(f"✗ 处理JSON文件失败: {e}")
            return {'success': 0, 'failed': 0}


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python database_inserter_async.py <json_file_path> [--create-schema]")
        print("示例: python database_inserter_async.py tools_info/toolify/single_test_result.json")
        print("创建架构: python database_inserter_async.py data.json --create-schema")
        sys.exit(1)

    json_file_path = sys.argv[1]
    create_schema = '--create-schema' in sys.argv

    if not os.path.exists(json_file_path):
        print(f"✗ 文件不存在: {json_file_path}")
        sys.exit(1)

    print("开始规范化数据库插入...")
    print(f"目标文件: {json_file_path}")
    if create_schema:
        print("将创建新的数据库架构")

    # 初始化数据库插入器
    inserter = AsyncDatabaseInserter()

    try:
        # 连接数据库
        await inserter.connect_to_database()

        # 如果需要创建架构
        if create_schema:
            schema_file = os.path.join(os.path.dirname(__file__), '..', 'backend', 'create_schema_simple.sql')
            if os.path.exists(schema_file):
                print("正在创建新的数据库架构...")
                await inserter.execute_schema_file(schema_file)
                # 重新加载缓存
                await inserter.load_category_cache()
                await inserter.load_tag_cache()
            else:
                print(f"✗ 架构文件不存在: {schema_file}")
                sys.exit(1)

        # 检查核心表是否存在
        if not await inserter.check_table_exists('tools'):
            print("✗ tools表不存在，请使用 --create-schema 参数创建数据库架构")
            sys.exit(1)

        if not await inserter.check_table_exists('categories'):
            print("✗ categories表不存在，请使用 --create-schema 参数创建数据库架构")
            sys.exit(1)

        if not await inserter.check_table_exists('tool_translations'):
            print("✗ tool_translations表不存在，请使用 --create-schema 参数创建数据库架构")
            sys.exit(1)

        print("✓ 数据库架构验证通过")

        # 插入数据
        result = await inserter.load_and_insert_json_file(json_file_path)

        print("\n=== 插入结果 ===")
        print(f"成功插入: {result['success']} 条")
        print(f"插入失败: {result['failed']} 条")

        if result['success'] > 0:
            print("✓ 数据插入完成")

            # 刷新视图（如果有物化视图的话）
            try:
                # 这里可以添加刷新物化视图的逻辑
                print("✓ 数据插入完成，无需刷新视图")
            except Exception as e:
                print(f"⚠️  刷新视图失败: {e}")
        else:
            print("✗ 没有成功插入任何数据")

    finally:
        await inserter.close_connection()


if __name__ == "__main__":
    asyncio.run(main())
